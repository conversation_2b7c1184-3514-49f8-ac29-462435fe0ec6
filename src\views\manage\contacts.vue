<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
            <el-form-item label="联系人姓名" prop="name">
                <el-input v-model="queryParams.name" placeholder="请输入联系人姓名" @keyup.enter="getList" />
            </el-form-item>
            <el-form-item label="单位名称" prop="unitName">
                <el-input v-model="queryParams.unitName" placeholder="请输入单位名称" @keyup.enter="getList" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="getList">搜索</el-button>
                <el-button icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="list">
            <el-table-column label="头像" align="center" width="120">
                <template #default="scope">
                    <el-avatar :size="100" :src="scope.row.avatar" fit="cover">
                        <img src="https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png" />
                    </el-avatar>
                </template>
            </el-table-column>
            <el-table-column label="联系人姓名" align="center" prop="name" />
            <el-table-column label="性别" align="center" width="80">
                <template #default="scope">
                    <el-tag :type="scope.row.gender === 1 ? 'primary' : 'danger'">
                        {{ scope.row.gender === 1 ? '男' : '女' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="单位名称" align="center" prop="unitName" />
            <el-table-column label="微信号" align="center" prop="wechatId" />
            <el-table-column label="类型" align="center">
                <template #default="scope">
                    {{ scope.row.type === 1 ? '男' : '女' }}
                </template>
            </el-table-column>
            <el-table-column label="操作" width="280" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button link type="primary" icon="Edit" @click="handleEdit(scope.row)">编辑</el-button>
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改联系人对话框 -->
        <el-dialog :title="title" v-model="dialogVisible" width="600px" append-to-body @close="handleClose">
            <el-form ref="dataForm" :model="dataForm" :rules="dataRules" label-width="120px">
                <el-form-item label="头像" prop="avatar">
                    <el-upload class="avatar-uploader" action="#" :show-file-list="false"
                        :before-upload="beforeAvatarUpload" :http-request="handleAvatarUpload">
                        <img v-if="dataForm.avatar" :src="dataForm.avatar" class="avatar" />
                        <el-icon v-else class="avatar-uploader-icon">
                            <Plus />
                        </el-icon>
                    </el-upload>
                </el-form-item>
                <el-form-item label="联系人姓名" prop="name">
                    <el-input v-model="dataForm.name" placeholder="请输入联系人姓名" />
                </el-form-item>
                <el-form-item label="性别" prop="gender">
                    <el-radio-group v-model="dataForm.gender">
                        <el-radio :label="1">男</el-radio>
                        <el-radio :label="2">女</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="单位名称" prop="unitName">
                    <el-input v-model="dataForm.unitName" placeholder="请输入单位名称" />
                </el-form-item>
                <el-form-item label="微信号" prop="wechatId">
                    <el-input v-model="dataForm.wechatId" placeholder="请输入微信号" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="handleClose">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import { getList, addcontacts, updatecontacts, getcontacts, delcontacts } from "@/api/manage/contacts";
import { Plus } from '@element-plus/icons-vue';
export default {
    components: {
        Plus
    },
    data() {
        return {
            showSearch: true,
            isChange: false,
            list: [],
            loading: false,
            queryParams: {
                unitName: '',
                name: '',
                pageNum: 1,
                pageSize: 10
            },
            total: 0,
            dialogVisible: false,
            title: '',
            editId: null,
            dataForm: {
                avatar: "",
                unitName: "",
                name: "",
                gender: 1,
                wechatId: ""
            },
            dataRules: {
                avatar: [
                    { required: true, message: '头像不能为空', trigger: 'change' }
                ],
                unitName: [
                    { required: true, message: '单位名称不能为空', trigger: 'blur' }
                ],
                name: [
                    { required: true, message: '联系人姓名不能为空', trigger: 'blur' }
                ],
                gender: [
                    { required: true, message: '性别不能为空', trigger: 'change' }
                ],
                wechatId: [
                    { required: true, message: '微信号不能为空', trigger: 'blur' }
                ]
            }
        }
    },
    mounted() {
        this.getList()
    },
    methods: {
        beforeAvatarUpload(file) {
            const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
            const isLt2M = file.size / 1024 / 1024 < 2;

            if (!isJPG) {
                this.$message.error('上传头像图片只能是 JPG/PNG 格式!');
            }
            if (!isLt2M) {
                this.$message.error('上传头像图片大小不能超过 2MB!');
            }
            return isJPG && isLt2M;
        },
        handleAvatarUpload(options) {
            const file = options.file;
            const reader = new FileReader();
            reader.onload = (e) => {
                this.dataForm.avatar = e.target.result;
            };
            reader.readAsDataURL(file);
        },
        submitForm() {
            this.$refs.dataForm.validate(async (valid) => {
                if (valid) {
                    if (this.isChange) {
                        let dataForm = JSON.parse(JSON.stringify(this.dataForm));
                        dataForm.id = this.editId;
                        let res = await updatecontacts(dataForm)
                        if (res.code == 200) {
                            this.$message({
                                message: '修改成功',
                                type: 'success'
                            });
                            this.getList()
                            this.dialogVisible = false
                        }
                    } else {
                        let dataForm = JSON.parse(JSON.stringify(this.dataForm));
                        let res = await addcontacts(dataForm)
                        if (res.code == 200) {
                            this.$message({
                                message: '新增成功',
                                type: 'success'
                            });
                            this.getList()
                            this.dialogVisible = false
                        }
                    }
                } else {
                    return false;
                }
            });
        },
        handleAdd() {
            this.isChange = false;
            this.title = "新增联系人";
            this.dialogVisible = true;
            this.$nextTick(() => {
                this.$refs['dataForm'].resetFields();
                this.dataForm = JSON.parse(JSON.stringify(this.$options.data().dataForm))
            })
        },
        handleClose() {
            this.$refs['dataForm'].resetFields();
            this.dataForm = JSON.parse(JSON.stringify(this.$options.data().dataForm))
            this.dialogVisible = false;
        },
        async handleEdit(val) {
            this.title = "修改联系人";
            let res = await getcontacts(val.id)
            if (res.code == 200) {
                this.dataForm = res.data;
                this.editId = val.id; // 设置编辑时的ID
            }
            this.dialogVisible = true;
            this.isChange = true;
        },
        handleDelete(val) {
            this.$confirm('确认删除吗？')
                .then(async (_) => {
                    let res = await delcontacts(val.id)
                    if (res.code == 200) {
                        this.$message({
                            message: '删除成功',
                            type: 'success'
                        });
                        this.getList()
                    }
                })
        },
        reset() {
            this.queryParams = {
                unitName: '',
                name: '',
                pageNum: 1,
                pageSize: 10
            };
            this.getList()
        },
        async getList() {
            this.loading = true;
            let res = await getList(this.queryParams)
            if (res.code == 200) {
                this.total = res.total;
                this.list = res.rows;
                this.loading = false;
            }
        }
    },
}

</script>
<style lang="scss" scoped>
:deep(.el-dialog) {
    background: #FFFFFF;
    border-radius: 14px;
    position: relative;
}

.avatar-uploader :deep(.el-upload) {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
}

.avatar-uploader :deep(.el-upload:hover) {
    border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    text-align: center;
}

.avatar {
    width: 100px;
    height: 100px;
    display: block;
}
</style>
