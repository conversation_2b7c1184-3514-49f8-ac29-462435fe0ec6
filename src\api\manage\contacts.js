import request from '@/utils/request'

// 查询联系人列表
export function getList(query) {
  return request({
    url: '/wechat/user/getUserList',
    method: 'get',
    params: query
  })
}

// 查询联系人详细
export function getcontacts(contactsId) {
  return request({
    url: '/wechat/user/' + contactsId,
    method: 'get'
  })
}

// 新增联系人
export function addcontacts(data) {
  return request({
    url: '/wechat/user',
    method: 'post',
    data: data
  })
}

// 修改联系人
export function updatecontacts(data) {
  return request({
    url: '/wechat/user',
    method: 'put',
    data: data
  })
}

// 删除联系人
export function delcontacts(contactsId) {
  return request({
    url: '/wechat/user/' + contactsId,
    method: 'delete'
  })
}
